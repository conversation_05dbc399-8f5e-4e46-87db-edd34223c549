use crate::*;
use crate::error::{CtpError, CtpResult, DisconnectReason};
use crate::types::{StringConvert, Direction, OffsetFlag, OrderPriceType, TimeCondition, VolumeCondition, ContingentCondition};
use std::ffi::{CStr, CString};
use std::ptr::NonNull;
use std::sync::{Arc, Mutex};
use std::os::raw::{c_char, c_int, c_void};

/// 交易回调事件
#[derive(Debug, Clone)]
pub enum TraderEvent {
    /// 前置连接
    FrontConnected,
    /// 前置断开
    FrontDisconnected(DisconnectReason),
    /// 心跳超时警告
    HeartBeatWarning(i32),
    /// 客户端认证响应
    Authenticate {
        auth_info: Option<AuthenticateInfo>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 登录响应
    UserLogin {
        login_info: Option<UserLoginInfo>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 登出响应
    UserLogout {
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 报单录入响应
    OrderInsert {
        order: Option<InputOrder>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 报单操作响应
    OrderAction {
        order_action: Option<InputOrderAction>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 查询报单响应
    QryOrder {
        order: Option<Order>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 查询成交响应
    QryTrade {
        trade: Option<Trade>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 查询投资者持仓响应
    QryInvestorPosition {
        position: Option<InvestorPosition>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 查询资金账户响应
    QryTradingAccount {
        account: Option<TradingAccount>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 查询合约响应
    QryInstrument {
        instrument: Option<Instrument>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 报单通知
    RtnOrder(Order),
    /// 成交通知
    RtnTrade(Trade),
    /// 错误响应
    Error {
        error_info: ErrorInfo,
        request_id: i32,
        is_last: bool,
    },
}

/// 认证信息
#[derive(Debug, Clone)]
pub struct AuthenticateInfo {
    pub broker_id: String,
    pub user_id: String,
    pub user_product_info: String,
    pub app_id: String,
    pub app_type: u8,
}

/// 用户登录信息
#[derive(Debug, Clone)]
pub struct UserLoginInfo {
    pub trading_day: String,
    pub login_time: String,
    pub broker_id: String,
    pub user_id: String,
    pub system_name: String,
    pub front_id: i32,
    pub session_id: i32,
    pub max_order_ref: String,
    pub shfe_time: String,
    pub dce_time: String,
    pub czce_time: String,
    pub ffex_time: String,
    pub ine_time: String,
    pub sys_version: String,
    pub gfex_time: String,
}

/// 错误信息
#[derive(Debug, Clone)]
pub struct ErrorInfo {
    pub error_id: i32,
    pub error_msg: String,
}

/// 报单录入
#[derive(Debug, Clone)]
pub struct InputOrder {
    pub broker_id: String,
    pub investor_id: String,
    pub instrument_id: String,
    pub order_ref: String,
    pub user_id: String,
    pub order_price_type: OrderPriceType,
    pub direction: Direction,
    pub comb_offset_flag: String,
    pub comb_hedge_flag: String,
    pub limit_price: f64,
    pub volume_total_original: i32,
    pub time_condition: TimeCondition,
    pub gtd_date: String,
    pub volume_condition: VolumeCondition,
    pub min_volume: i32,
    pub contingent_condition: ContingentCondition,
    pub stop_price: f64,
    pub force_close_reason: u8,
    pub is_auto_suspend: i32,
    pub business_unit: String,
    pub request_id: i32,
    pub user_force_close: i32,
    pub is_swap_order: i32,
    pub exchange_id: String,
    pub invest_unit_id: String,
    pub account_id: String,
    pub currency_id: String,
    pub client_id: String,
    pub ip_address: String,
    pub mac_address: String,
}

/// 报单操作
#[derive(Debug, Clone)]
pub struct InputOrderAction {
    pub broker_id: String,
    pub investor_id: String,
    pub order_action_ref: i32,
    pub order_ref: String,
    pub request_id: i32,
    pub front_id: i32,
    pub session_id: i32,
    pub exchange_id: String,
    pub order_sys_id: String,
    pub action_flag: u8,
    pub limit_price: f64,
    pub volume_change: i32,
    pub user_id: String,
    pub instrument_id: String,
    pub invest_unit_id: String,
    pub ip_address: String,
    pub mac_address: String,
}

/// 报单
#[derive(Debug, Clone)]
pub struct Order {
    pub broker_id: String,
    pub investor_id: String,
    pub instrument_id: String,
    pub order_ref: String,
    pub user_id: String,
    pub order_price_type: OrderPriceType,
    pub direction: Direction,
    pub comb_offset_flag: String,
    pub comb_hedge_flag: String,
    pub limit_price: f64,
    pub volume_total_original: i32,
    pub time_condition: TimeCondition,
    pub gtd_date: String,
    pub volume_condition: VolumeCondition,
    pub min_volume: i32,
    pub contingent_condition: ContingentCondition,
    pub stop_price: f64,
    pub force_close_reason: u8,
    pub is_auto_suspend: i32,
    pub business_unit: String,
    pub request_id: i32,
    pub exchange_id: String,
    pub order_sys_id: String,
    pub order_status: u8,
    pub order_type: u8,
    pub volume_traded: i32,
    pub volume_total: i32,
    pub insert_date: String,
    pub insert_time: String,
    pub active_time: String,
    pub suspend_time: String,
    pub update_time: String,
    pub cancel_time: String,
    pub active_trader_id: String,
    pub clearing_part_id: String,
    pub sequence_no: i32,
    pub front_id: i32,
    pub session_id: i32,
    pub user_product_info: String,
    pub status_msg: String,
    pub active_user_id: String,
    pub broker_order_seq: i32,
    pub relative_order_sys_id: String,
    pub zcetotal_traded_volume: i32,
    pub branch_id: String,
    pub invest_unit_id: String,
    pub account_id: String,
    pub currency_id: String,
    pub ip_address: String,
    pub mac_address: String,
}

/// 成交
#[derive(Debug, Clone)]
pub struct Trade {
    pub broker_id: String,
    pub investor_id: String,
    pub instrument_id: String,
    pub order_ref: String,
    pub user_id: String,
    pub exchange_id: String,
    pub trade_id: String,
    pub direction: Direction,
    pub order_sys_id: String,
    pub participant_id: String,
    pub client_id: String,
    pub trading_role: u8,
    pub exchange_inst_id: String,
    pub offset_flag: OffsetFlag,
    pub hedge_flag: u8,
    pub price: f64,
    pub volume: i32,
    pub trade_date: String,
    pub trade_time: String,
    pub trade_type: u8,
    pub price_source: u8,
    pub trader_id: String,
    pub order_local_id: String,
    pub clearing_part_id: String,
    pub business_unit: String,
    pub sequence_no: i32,
    pub trading_day: String,
    pub settlement_id: i32,
    pub broker_order_seq: i32,
    pub trade_source: u8,
    pub invest_unit_id: String,
}

/// 投资者持仓
#[derive(Debug, Clone)]
pub struct InvestorPosition {
    pub instrument_id: String,
    pub broker_id: String,
    pub investor_id: String,
    pub position_direction: u8,
    pub hedge_flag: u8,
    pub position_date: u8,
    pub yd_position: i32,
    pub position: i32,
    pub long_frozen: i32,
    pub short_frozen: i32,
    pub long_frozen_amount: f64,
    pub short_frozen_amount: f64,
    pub open_volume: i32,
    pub close_volume: i32,
    pub open_amount: f64,
    pub close_amount: f64,
    pub position_cost: f64,
    pub pre_margin: f64,
    pub use_margin: f64,
    pub frozen_margin: f64,
    pub frozen_cash: f64,
    pub frozen_commission: f64,
    pub cash_in: f64,
    pub commission: f64,
    pub close_profit: f64,
    pub position_profit: f64,
    pub pre_settlement_price: f64,
    pub settlement_price: f64,
    pub trading_day: String,
    pub settlement_id: i32,
    pub open_cost: f64,
    pub exchange_margin: f64,
    pub comb_position: i32,
    pub comb_long_frozen: i32,
    pub comb_short_frozen: i32,
    pub close_profit_by_date: f64,
    pub close_profit_by_trade: f64,
    pub today_position: i32,
    pub margin_rate_by_money: f64,
    pub margin_rate_by_volume: f64,
    pub strike_frozen: i32,
    pub strike_frozen_amount: f64,
    pub abandon_frozen: i32,
    pub exchange_id: String,
    pub yd_strike_frozen: i32,
    pub invest_unit_id: String,
    pub position_cost_offset: f64,
    pub tas_position: i32,
    pub tas_position_cost: f64,
}

/// 资金账户
#[derive(Debug, Clone)]
pub struct TradingAccount {
    pub broker_id: String,
    pub account_id: String,
    pub pre_mortgage: f64,
    pub pre_credit: f64,
    pub pre_deposit: f64,
    pub pre_balance: f64,
    pub pre_margin: f64,
    pub interest_base: f64,
    pub interest: f64,
    pub deposit: f64,
    pub withdraw: f64,
    pub frozen_margin: f64,
    pub frozen_cash: f64,
    pub frozen_commission: f64,
    pub curr_margin: f64,
    pub cash_in: f64,
    pub commission: f64,
    pub close_profit: f64,
    pub position_profit: f64,
    pub balance: f64,
    pub available: f64,
    pub withdraw_quota: f64,
    pub reserve: f64,
    pub trading_day: String,
    pub settlement_id: i32,
    pub credit: f64,
    pub mortgage: f64,
    pub exchange_margin: f64,
    pub delivery_margin: f64,
    pub exchange_delivery_margin: f64,
    pub reserve_balance: f64,
    pub currency_id: String,
    pub pre_fund_mortgage_in: f64,
    pub pre_fund_mortgage_out: f64,
    pub fund_mortgage_in: f64,
    pub fund_mortgage_out: f64,
    pub fund_mortgage_available: f64,
    pub mortgage_able_fund: f64,
    pub spec_product_margin: f64,
    pub spec_product_frozen_margin: f64,
    pub spec_product_commission: f64,
    pub spec_product_frozen_commission: f64,
    pub spec_product_position_profit: f64,
    pub spec_product_close_profit: f64,
    pub spec_product_position_profit_by_alg: f64,
    pub spec_product_exchange_margin: f64,
    pub bis_margin: f64,
    pub bis_frozen_margin: f64,
    pub bis_commission: f64,
    pub bis_frozen_commission: f64,
    pub bis_position_profit: f64,
    pub bis_close_profit: f64,
    pub bis_position_profit_by_alg: f64,
    pub bis_exchange_margin: f64,
    pub frozen_swap: f64,
    pub remain_swap: f64,
}

/// 合约
#[derive(Debug, Clone)]
pub struct Instrument {
    pub instrument_id: String,
    pub exchange_id: String,
    pub instrument_name: String,
    pub exchange_inst_id: String,
    pub product_id: String,
    pub product_class: u8,
    pub delivery_year: i32,
    pub delivery_month: i32,
    pub max_market_order_volume: i32,
    pub min_market_order_volume: i32,
    pub max_limit_order_volume: i32,
    pub min_limit_order_volume: i32,
    pub volume_multiple: i32,
    pub price_tick: f64,
    pub create_date: String,
    pub open_date: String,
    pub expire_date: String,
    pub start_deliv_date: String,
    pub end_deliv_date: String,
    pub inst_life_phase: u8,
    pub is_trading: i32,
    pub position_type: u8,
    pub position_date_type: u8,
    pub long_margin_ratio: f64,
    pub short_margin_ratio: f64,
    pub max_margin_side_algorithm: u8,
    pub underlying_inst_id: String,
    pub strike_price: f64,
    pub options_type: u8,
    pub underlying_multiple: f64,
    pub combination_type: u8,
}

/// 交易回调处理器
pub trait TraderSpiHandler: Send + Sync {
    fn on_event(&self, event: TraderEvent);
}

/// 交易 API
pub struct TraderApi {
    inner: NonNull<ffi::CThostFtdcTraderApi>,
    handler: Option<Arc<dyn TraderSpiHandler>>,
    request_id: Arc<Mutex<i32>>,
}

unsafe impl Send for TraderApi {}
unsafe impl Sync for TraderApi {}

impl TraderApi {
    /// 创建交易 API
    pub fn new(flow_path: &str) -> CtpResult<Self> {
        let c_flow_path = flow_path.to_cstring()?;

        unsafe {
            let api_ptr = ffi::CThostFtdcTraderApi_CreateFtdcTraderApi(c_flow_path.as_ptr());

            if api_ptr.is_null() {
                return Err(CtpError::InitializationFailed);
            }

            Ok(Self {
                inner: NonNull::new_unchecked(api_ptr),
                handler: None,
                request_id: Arc::new(Mutex::new(1)),
            })
        }
    }

    /// 获取 API 版本
    pub fn get_api_version() -> String {
        unsafe {
            let version = ffi::CThostFtdcTraderApi_GetApiVersion();
            String::from_cstr(CStr::from_ptr(version))
        }
    }
    
    /// 设置回调处理器
    pub fn set_spi_handler(&mut self, handler: Arc<dyn TraderSpiHandler>) {
        self.handler = Some(handler);
        // TODO: 注册 SPI 回调
    }
    
    /// 初始化
    pub fn init(&self) {
        unsafe {
            ffi::CThostFtdcTraderApi_Init(self.inner.as_ptr());
        }
    }

    /// 等待线程结束
    pub fn join(&self) -> i32 {
        unsafe {
            ffi::CThostFtdcTraderApi_Join(self.inner.as_ptr())
        }
    }

    /// 获取交易日
    pub fn get_trading_day(&self) -> String {
        unsafe {
            let trading_day = ffi::CThostFtdcTraderApi_GetTradingDay(self.inner.as_ptr());
            String::from_cstr(CStr::from_ptr(trading_day))
        }
    }

    /// 注册前置机地址
    pub fn register_front(&self, front_address: &str) -> CtpResult<()> {
        let c_address = front_address.to_cstring()?;
        unsafe {
            ffi::CThostFtdcTraderApi_RegisterFront(self.inner.as_ptr(), c_address.as_ptr() as *mut _);
        }
        Ok(())
    }

    /// 注册名字服务器地址
    pub fn register_name_server(&self, ns_address: &str) -> CtpResult<()> {
        let c_address = ns_address.to_cstring()?;
        unsafe {
            ffi::CThostFtdcTraderApi_RegisterNameServer(self.inner.as_ptr(), c_address.as_ptr() as *mut _);
        }
        Ok(())
    }
    
    /// 客户端认证
    pub fn req_authenticate(&self, broker_id: &str, user_id: &str, user_product_info: &str, auth_code: &str, app_id: &str) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造认证请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 用户登录
    pub fn req_user_login(&self, broker_id: &str, user_id: &str, password: &str) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造登录请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 用户登出
    pub fn req_user_logout(&self, broker_id: &str, user_id: &str) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造登出请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 报单录入
    pub fn req_order_insert(&self, order: &InputOrder) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造报单录入请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 报单操作
    pub fn req_order_action(&self, order_action: &InputOrderAction) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造报单操作请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 查询报单
    pub fn req_qry_order(&self, broker_id: &str, investor_id: &str, instrument_id: Option<&str>, exchange_id: Option<&str>, order_sys_id: Option<&str>, insert_time_start: Option<&str>, insert_time_end: Option<&str>) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造查询报单请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 查询成交
    pub fn req_qry_trade(&self, broker_id: &str, investor_id: &str, instrument_id: Option<&str>, exchange_id: Option<&str>, trade_id: Option<&str>, trade_time_start: Option<&str>, trade_time_end: Option<&str>) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造查询成交请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 查询投资者持仓
    pub fn req_qry_investor_position(&self, broker_id: &str, investor_id: &str, instrument_id: Option<&str>, exchange_id: Option<&str>) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造查询持仓请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 查询资金账户
    pub fn req_qry_trading_account(&self, broker_id: &str, investor_id: &str, currency_id: Option<&str>, bis_type: Option<u8>) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造查询资金账户请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 查询合约
    pub fn req_qry_instrument(&self, instrument_id: Option<&str>, exchange_id: Option<&str>, exchange_inst_id: Option<&str>, product_id: Option<&str>) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造查询合约请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 获取下一个请求 ID
    fn next_request_id(&self) -> i32 {
        let mut id = self.request_id.lock().unwrap();
        let current = *id;
        *id += 1;
        current
    }
}

impl Drop for TraderApi {
    fn drop(&mut self) {
        unsafe {
            ffi::CThostFtdcTraderApi_Release(self.inner.as_ptr());
        }
    }
}
