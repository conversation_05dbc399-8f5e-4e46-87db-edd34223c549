use std::ffi::{CStr, CString};
use crate::error::{CtpError, CtpResult};

/// 字符串转换工具
pub trait StringConvert {
    fn to_cstring(&self) -> CtpResult<CString>;
    fn from_cstr(cstr: &CStr) -> String;
    fn from_bytes(bytes: &[u8]) -> String;
}

impl StringConvert for str {
    fn to_cstring(&self) -> CtpResult<CString> {
        CString::new(self).map_err(|e| CtpError::StringConversionFailed(e.to_string()))
    }
    
    fn from_cstr(cstr: &CStr) -> String {
        cstr.to_string_lossy().into_owned()
    }
    
    fn from_bytes(bytes: &[u8]) -> String {
        // 找到第一个空字节的位置
        let end = bytes.iter().position(|&b| b == 0).unwrap_or(bytes.len());
        String::from_utf8_lossy(&bytes[..end]).into_owned()
    }
}

impl StringConvert for String {
    fn to_cstring(&self) -> CtpResult<CString> {
        self.as_str().to_cstring()
    }
    
    fn from_cstr(cstr: &CStr) -> String {
        str::from_cstr(cstr)
    }
    
    fn from_bytes(bytes: &[u8]) -> String {
        str::from_bytes(bytes)
    }
}

/// 买卖方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// 买
    Buy = b'0' as isize,
    /// 卖
    Sell = b'1' as isize,
}

impl From<u8> for Direction {
    fn from(value: u8) -> Self {
        match value {
            b'0' => Direction::Buy,
            b'1' => Direction::Sell,
            _ => Direction::Buy, // 默认值
        }
    }
}

impl From<Direction> for u8 {
    fn from(direction: Direction) -> Self {
        direction as u8
    }
}

/// 开平标志
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OffsetFlag {
    /// 开仓
    Open = b'0' as isize,
    /// 平仓
    Close = b'1' as isize,
    /// 强平
    ForceClose = b'2' as isize,
    /// 平今
    CloseToday = b'3' as isize,
    /// 平昨
    CloseYesterday = b'4' as isize,
    /// 强减
    ForceOff = b'5' as isize,
    /// 本地强平
    LocalForceClose = b'6' as isize,
}

impl From<u8> for OffsetFlag {
    fn from(value: u8) -> Self {
        match value {
            b'0' => OffsetFlag::Open,
            b'1' => OffsetFlag::Close,
            b'2' => OffsetFlag::ForceClose,
            b'3' => OffsetFlag::CloseToday,
            b'4' => OffsetFlag::CloseYesterday,
            b'5' => OffsetFlag::ForceOff,
            b'6' => OffsetFlag::LocalForceClose,
            _ => OffsetFlag::Open, // 默认值
        }
    }
}

impl From<OffsetFlag> for u8 {
    fn from(flag: OffsetFlag) -> Self {
        flag as u8
    }
}

/// 报单价格条件
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OrderPriceType {
    /// 任意价
    AnyPrice = b'1' as isize,
    /// 限价
    LimitPrice = b'2' as isize,
    /// 最优价
    BestPrice = b'3' as isize,
    /// 最新价
    LastPrice = b'4' as isize,
    /// 最新价浮动上浮1个ticks
    LastPricePlusOneTicks = b'5' as isize,
    /// 最新价浮动上浮2个ticks
    LastPricePlusTwoTicks = b'6' as isize,
    /// 最新价浮动上浮3个ticks
    LastPricePlusThreeTicks = b'7' as isize,
    /// 卖一价
    AskPrice1 = b'8' as isize,
    /// 卖一价浮动上浮1个ticks
    AskPrice1PlusOneTicks = b'9' as isize,
    /// 卖一价浮动上浮2个ticks
    AskPrice1PlusTwoTicks = b'A' as isize,
    /// 卖一价浮动上浮3个ticks
    AskPrice1PlusThreeTicks = b'B' as isize,
    /// 买一价
    BidPrice1 = b'C' as isize,
    /// 买一价浮动上浮1个ticks
    BidPrice1PlusOneTicks = b'D' as isize,
    /// 买一价浮动上浮2个ticks
    BidPrice1PlusTwoTicks = b'E' as isize,
    /// 买一价浮动上浮3个ticks
    BidPrice1PlusThreeTicks = b'F' as isize,
    /// 五档价
    FiveLevelPrice = b'G' as isize,
}

impl From<u8> for OrderPriceType {
    fn from(value: u8) -> Self {
        match value {
            b'1' => OrderPriceType::AnyPrice,
            b'2' => OrderPriceType::LimitPrice,
            b'3' => OrderPriceType::BestPrice,
            b'4' => OrderPriceType::LastPrice,
            b'5' => OrderPriceType::LastPricePlusOneTicks,
            b'6' => OrderPriceType::LastPricePlusTwoTicks,
            b'7' => OrderPriceType::LastPricePlusThreeTicks,
            b'8' => OrderPriceType::AskPrice1,
            b'9' => OrderPriceType::AskPrice1PlusOneTicks,
            b'A' => OrderPriceType::AskPrice1PlusTwoTicks,
            b'B' => OrderPriceType::AskPrice1PlusThreeTicks,
            b'C' => OrderPriceType::BidPrice1,
            b'D' => OrderPriceType::BidPrice1PlusOneTicks,
            b'E' => OrderPriceType::BidPrice1PlusTwoTicks,
            b'F' => OrderPriceType::BidPrice1PlusThreeTicks,
            b'G' => OrderPriceType::FiveLevelPrice,
            _ => OrderPriceType::LimitPrice, // 默认值
        }
    }
}

impl From<OrderPriceType> for u8 {
    fn from(price_type: OrderPriceType) -> Self {
        price_type as u8
    }
}

/// 有效期类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TimeCondition {
    /// 立即完成，否则撤销
    IOC = b'1' as isize,
    /// 本节有效
    GFS = b'2' as isize,
    /// 当日有效
    GFD = b'3' as isize,
    /// 指定日期前有效
    GTD = b'4' as isize,
    /// 撤销前有效
    GTC = b'5' as isize,
    /// 集合竞价有效
    GFA = b'6' as isize,
}

impl From<u8> for TimeCondition {
    fn from(value: u8) -> Self {
        match value {
            b'1' => TimeCondition::IOC,
            b'2' => TimeCondition::GFS,
            b'3' => TimeCondition::GFD,
            b'4' => TimeCondition::GTD,
            b'5' => TimeCondition::GTC,
            b'6' => TimeCondition::GFA,
            _ => TimeCondition::GFD, // 默认值
        }
    }
}

impl From<TimeCondition> for u8 {
    fn from(condition: TimeCondition) -> Self {
        condition as u8
    }
}

/// 成交量类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VolumeCondition {
    /// 任何数量
    AV = b'1' as isize,
    /// 最小数量
    MV = b'2' as isize,
    /// 全部数量
    CV = b'3' as isize,
}

impl From<u8> for VolumeCondition {
    fn from(value: u8) -> Self {
        match value {
            b'1' => VolumeCondition::AV,
            b'2' => VolumeCondition::MV,
            b'3' => VolumeCondition::CV,
            _ => VolumeCondition::AV, // 默认值
        }
    }
}

impl From<VolumeCondition> for u8 {
    fn from(condition: VolumeCondition) -> Self {
        condition as u8
    }
}

/// 触发条件
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ContingentCondition {
    /// 立即
    Immediately = b'1' as isize,
    /// 止损
    Touch = b'2' as isize,
    /// 止赢
    TouchProfit = b'3' as isize,
    /// 预埋单
    ParkedOrder = b'4' as isize,
    /// 最新价大于条件价
    LastPriceGreaterThanStopPrice = b'5' as isize,
    /// 最新价大于等于条件价
    LastPriceGreaterEqualStopPrice = b'6' as isize,
    /// 最新价小于条件价
    LastPriceLesserThanStopPrice = b'7' as isize,
    /// 最新价小于等于条件价
    LastPriceLesserEqualStopPrice = b'8' as isize,
    /// 卖一价大于条件价
    AskPriceGreaterThanStopPrice = b'9' as isize,
    /// 卖一价大于等于条件价
    AskPriceGreaterEqualStopPrice = b'A' as isize,
    /// 卖一价小于条件价
    AskPriceLesserThanStopPrice = b'B' as isize,
    /// 卖一价小于等于条件价
    AskPriceLesserEqualStopPrice = b'C' as isize,
    /// 买一价大于条件价
    BidPriceGreaterThanStopPrice = b'D' as isize,
    /// 买一价大于等于条件价
    BidPriceGreaterEqualStopPrice = b'E' as isize,
    /// 买一价小于条件价
    BidPriceLesserThanStopPrice = b'F' as isize,
    /// 买一价小于等于条件价
    BidPriceLesserEqualStopPrice = b'H' as isize,
}

impl From<u8> for ContingentCondition {
    fn from(value: u8) -> Self {
        match value {
            b'1' => ContingentCondition::Immediately,
            b'2' => ContingentCondition::Touch,
            b'3' => ContingentCondition::TouchProfit,
            b'4' => ContingentCondition::ParkedOrder,
            b'5' => ContingentCondition::LastPriceGreaterThanStopPrice,
            b'6' => ContingentCondition::LastPriceGreaterEqualStopPrice,
            b'7' => ContingentCondition::LastPriceLesserThanStopPrice,
            b'8' => ContingentCondition::LastPriceLesserEqualStopPrice,
            b'9' => ContingentCondition::AskPriceGreaterThanStopPrice,
            b'A' => ContingentCondition::AskPriceGreaterEqualStopPrice,
            b'B' => ContingentCondition::AskPriceLesserThanStopPrice,
            b'C' => ContingentCondition::AskPriceLesserEqualStopPrice,
            b'D' => ContingentCondition::BidPriceGreaterThanStopPrice,
            b'E' => ContingentCondition::BidPriceGreaterEqualStopPrice,
            b'F' => ContingentCondition::BidPriceLesserThanStopPrice,
            b'H' => ContingentCondition::BidPriceLesserEqualStopPrice,
            _ => ContingentCondition::Immediately, // 默认值
        }
    }
}

impl From<ContingentCondition> for u8 {
    fn from(condition: ContingentCondition) -> Self {
        condition as u8
    }
}
