#ifndef CTP_WRAPPER_H
#define CTP_WRAPPER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

// 前向声明
typedef struct CThostFtdcMdApi CThostFtdcMdApi;
typedef struct CThostFtdcTraderApi CThostFtdcTraderApi;
typedef struct CThostFtdcMdSpi CThostFtdcMdSpi;
typedef struct CThostFtdcTraderSpi CThostFtdcTraderSpi;

// 结构体前向声明
typedef struct CThostFtdcReqUserLoginField CThostFtdcReqUserLoginField;
typedef struct CThostFtdcUserLogoutField CThostFtdcUserLogoutField;
typedef struct CThostFtdcReqAuthenticateField CThostFtdcReqAuthenticateField;
typedef struct CThostFtdcInputOrderField CThostFtdcInputOrderField;
typedef struct CThostFtdcInputOrderActionField CThostFtdcInputOrderActionField;
typedef struct CThostFtdcQryOrderField CThostFtdcQryOrderField;
typedef struct CThostFtdcQryTradeField CThostFtdcQryTradeField;
typedef struct CThostFtdcQryInvestorPositionField CThostFtdcQryInvestorPositionField;
typedef struct CThostFtdcQryTradingAccountField CThostFtdcQryTradingAccountField;
typedef struct CThostFtdcQryInstrumentField CThostFtdcQryInstrumentField;
typedef struct CThostFtdcSettlementInfoConfirmField CThostFtdcSettlementInfoConfirmField;

// ========== 行情 API 包装函数 ==========

// 创建行情 API
CThostFtdcMdApi* ctp_md_api_create(const char* flow_path, bool is_using_udp, bool is_multicast);

// 获取行情 API 版本
const char* ctp_md_api_get_version(void);

// 释放行情 API
void ctp_md_api_release(CThostFtdcMdApi* api);

// 初始化行情 API
void ctp_md_api_init(CThostFtdcMdApi* api);

// 等待线程结束
int ctp_md_api_join(CThostFtdcMdApi* api);

// 获取交易日
const char* ctp_md_api_get_trading_day(CThostFtdcMdApi* api);

// 注册前置机地址
void ctp_md_api_register_front(CThostFtdcMdApi* api, char* front_address);

// 注册名字服务器地址
void ctp_md_api_register_name_server(CThostFtdcMdApi* api, char* ns_address);

// 注册回调接口
void ctp_md_api_register_spi(CThostFtdcMdApi* api, CThostFtdcMdSpi* spi);

// 用户登录请求
int ctp_md_api_req_user_login(CThostFtdcMdApi* api, CThostFtdcReqUserLoginField* req, int request_id);

// 用户登出请求
int ctp_md_api_req_user_logout(CThostFtdcMdApi* api, CThostFtdcUserLogoutField* req, int request_id);

// 订阅行情
int ctp_md_api_subscribe_market_data(CThostFtdcMdApi* api, char* instrument_ids[], int count);

// 取消订阅行情
int ctp_md_api_unsubscribe_market_data(CThostFtdcMdApi* api, char* instrument_ids[], int count);

// 订阅询价
int ctp_md_api_subscribe_for_quote_rsp(CThostFtdcMdApi* api, char* instrument_ids[], int count);

// 取消订阅询价
int ctp_md_api_unsubscribe_for_quote_rsp(CThostFtdcMdApi* api, char* instrument_ids[], int count);

// ========== 交易 API 包装函数 ==========

// 创建交易 API
CThostFtdcTraderApi* ctp_trader_api_create(const char* flow_path);

// 获取交易 API 版本
const char* ctp_trader_api_get_version(void);

// 释放交易 API
void ctp_trader_api_release(CThostFtdcTraderApi* api);

// 初始化交易 API
void ctp_trader_api_init(CThostFtdcTraderApi* api);

// 等待线程结束
int ctp_trader_api_join(CThostFtdcTraderApi* api);

// 获取交易日
const char* ctp_trader_api_get_trading_day(CThostFtdcTraderApi* api);

// 注册前置机地址
void ctp_trader_api_register_front(CThostFtdcTraderApi* api, char* front_address);

// 注册名字服务器地址
void ctp_trader_api_register_name_server(CThostFtdcTraderApi* api, char* ns_address);

// 注册回调接口
void ctp_trader_api_register_spi(CThostFtdcTraderApi* api, CThostFtdcTraderSpi* spi);

// 客户端认证请求
int ctp_trader_api_req_authenticate(CThostFtdcTraderApi* api, CThostFtdcReqAuthenticateField* req, int request_id);

// 用户登录请求
int ctp_trader_api_req_user_login(CThostFtdcTraderApi* api, CThostFtdcReqUserLoginField* req, int request_id);

// 用户登出请求
int ctp_trader_api_req_user_logout(CThostFtdcTraderApi* api, CThostFtdcUserLogoutField* req, int request_id);

// 报单录入请求
int ctp_trader_api_req_order_insert(CThostFtdcTraderApi* api, CThostFtdcInputOrderField* req, int request_id);

// 报单操作请求
int ctp_trader_api_req_order_action(CThostFtdcTraderApi* api, CThostFtdcInputOrderActionField* req, int request_id);

// 查询报单
int ctp_trader_api_req_qry_order(CThostFtdcTraderApi* api, CThostFtdcQryOrderField* req, int request_id);

// 查询成交
int ctp_trader_api_req_qry_trade(CThostFtdcTraderApi* api, CThostFtdcQryTradeField* req, int request_id);

// 查询投资者持仓
int ctp_trader_api_req_qry_investor_position(CThostFtdcTraderApi* api, CThostFtdcQryInvestorPositionField* req, int request_id);

// 查询资金账户
int ctp_trader_api_req_qry_trading_account(CThostFtdcTraderApi* api, CThostFtdcQryTradingAccountField* req, int request_id);

// 查询合约
int ctp_trader_api_req_qry_instrument(CThostFtdcTraderApi* api, CThostFtdcQryInstrumentField* req, int request_id);

// 投资者结算结果确认
int ctp_trader_api_req_settlement_info_confirm(CThostFtdcTraderApi* api, CThostFtdcSettlementInfoConfirmField* req, int request_id);

#ifdef __cplusplus
}
#endif

#endif // CTP_WRAPPER_H
