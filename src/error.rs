use thiserror::Error;

/// CTP API 错误类型
#[derive(Error, Debug)]
pub enum CtpError {
    #[error("API 初始化失败")]
    InitializationFailed,
    
    #[error("连接失败: {0}")]
    ConnectionFailed(String),
    
    #[error("登录失败: 错误代码 {error_id}, 错误信息: {error_msg}")]
    LoginFailed {
        error_id: i32,
        error_msg: String,
    },
    
    #[error("认证失败: 错误代码 {error_id}, 错误信息: {error_msg}")]
    AuthenticationFailed {
        error_id: i32,
        error_msg: String,
    },
    
    #[error("订阅行情失败: {0}")]
    SubscriptionFailed(String),
    
    #[error("下单失败: 错误代码 {error_id}, 错误信息: {error_msg}")]
    OrderFailed {
        error_id: i32,
        error_msg: String,
    },
    
    #[error("查询失败: 错误代码 {error_id}, 错误信息: {error_msg}")]
    QueryFailed {
        error_id: i32,
        error_msg: String,
    },
    
    #[error("字符串转换失败: {0}")]
    StringConversionFailed(String),
    
    #[error("空指针错误")]
    NullPointer,
    
    #[error("超时错误")]
    Timeout,
    
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// CTP API 结果类型
pub type CtpResult<T> = Result<T, CtpError>;

/// 从 CTP 响应信息创建错误
pub fn from_rsp_info(error_id: i32, error_msg: &str) -> CtpError {
    match error_id {
        0 => return CtpError::Unknown("No error but treated as error".to_string()),
        3 => CtpError::LoginFailed {
            error_id,
            error_msg: error_msg.to_string(),
        },
        63 => CtpError::AuthenticationFailed {
            error_id,
            error_msg: error_msg.to_string(),
        },
        _ => CtpError::Unknown(format!("错误代码: {}, 错误信息: {}", error_id, error_msg)),
    }
}

/// 检查响应信息是否包含错误
pub fn check_rsp_info(error_id: i32, error_msg: &str) -> CtpResult<()> {
    if error_id != 0 {
        Err(from_rsp_info(error_id, error_msg))
    } else {
        Ok(())
    }
}

/// 连接断开原因
#[derive(Debug, Clone, Copy)]
pub enum DisconnectReason {
    /// 网络读失败
    NetworkReadFailed = 0x1001,
    /// 网络写失败
    NetworkWriteFailed = 0x1002,
    /// 接收心跳超时
    HeartbeatTimeout = 0x2001,
    /// 发送心跳失败
    HeartbeatSendFailed = 0x2002,
    /// 收到错误报文
    ErrorMessage = 0x2003,
    /// 未知原因
    Unknown,
}

impl From<i32> for DisconnectReason {
    fn from(reason: i32) -> Self {
        match reason {
            0x1001 => DisconnectReason::NetworkReadFailed,
            0x1002 => DisconnectReason::NetworkWriteFailed,
            0x2001 => DisconnectReason::HeartbeatTimeout,
            0x2002 => DisconnectReason::HeartbeatSendFailed,
            0x2003 => DisconnectReason::ErrorMessage,
            _ => DisconnectReason::Unknown,
        }
    }
}

impl std::fmt::Display for DisconnectReason {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let msg = match self {
            DisconnectReason::NetworkReadFailed => "网络读失败",
            DisconnectReason::NetworkWriteFailed => "网络写失败",
            DisconnectReason::HeartbeatTimeout => "接收心跳超时",
            DisconnectReason::HeartbeatSendFailed => "发送心跳失败",
            DisconnectReason::ErrorMessage => "收到错误报文",
            DisconnectReason::Unknown => "未知原因",
        };
        write!(f, "{}", msg)
    }
}
