use autocxx::prelude::*;

include_cpp! {
    #include "ThostFtdcMdApi.h"
    #include "ThostFtdcTraderApi.h"

    safety!(unsafe_ffi)

    // 基本API类
    generate!("CThostFtdcMdApi")
    generate!("CThostFtdcTraderApi")
}

pub use ffi::*;

// 模块导出
pub mod md_api;
pub mod trader_api;
pub mod simple_example;
pub mod types;
pub mod error;

// 导出主要类型，避免重复导出
pub use error::*;
pub use types::*;
pub use simple_example::*;

// 有选择地导出 API，避免名称冲突
pub use md_api::{MdApi, MdEvent, MdSpiHandler};
pub use trader_api::{TraderApi, TraderEvent, TraderSpiHandler};
