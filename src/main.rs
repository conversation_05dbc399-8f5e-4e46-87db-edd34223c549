use rctp::*;
use std::sync::Arc;
use std::time::Duration;
use std::thread;

/// 行情回调处理器示例
struct MyMdSpiHandler;

impl MdSpiHandler for MyMdSpiHandler {
    fn on_event(&self, event: MdEvent) {
        match event {
            MdEvent::FrontConnected => {
                println!("行情前置连接成功");
            }
            MdEvent::FrontDisconnected(reason) => {
                println!("行情前置断开连接: {}", reason);
            }
            MdEvent::HeartBeatWarning(time_lapse) => {
                println!("行情心跳超时警告: {} 秒", time_lapse);
            }
            MdEvent::UserLogin { login_info, error_info, request_id, is_last } => {
                if let Some(error) = error_info {
                    println!("行情登录失败: {} - {}", error.error_id, error.error_msg);
                } else if let Some(info) = login_info {
                    println!("行情登录成功: 交易日={}, 前置编号={}, 会话编号={}",
                             info.trading_day, info.front_id, info.session_id);
                }
            }
            MdEvent::DepthMarketData(data) => {
                println!("收到行情数据: {} 最新价={:.2} 成交量={} 时间={}",
                         data.instrument_id, data.last_price, data.volume, data.update_time);
            }
            MdEvent::SubMarketData { instrument_id, error_info, .. } => {
                if let Some(error) = error_info {
                    println!("订阅行情失败: {} - {}", error.error_id, error.error_msg);
                } else {
                    println!("订阅行情成功: {}", instrument_id);
                }
            }
            _ => {
                println!("收到其他行情事件: {:?}", event);
            }
        }
    }
}

/// 交易回调处理器示例
struct MyTraderSpiHandler;

impl TraderSpiHandler for MyTraderSpiHandler {
    fn on_event(&self, event: TraderEvent) {
        match event {
            TraderEvent::FrontConnected => {
                println!("交易前置连接成功");
            }
            TraderEvent::FrontDisconnected(reason) => {
                println!("交易前置断开连接: {}", reason);
            }
            TraderEvent::Authenticate { auth_info, error_info, .. } => {
                if let Some(error) = error_info {
                    println!("客户端认证失败: {} - {}", error.error_id, error.error_msg);
                } else if let Some(info) = auth_info {
                    println!("客户端认证成功: 经纪公司={}, 用户={}", info.broker_id, info.user_id);
                }
            }
            TraderEvent::UserLogin { login_info, error_info, .. } => {
                if let Some(error) = error_info {
                    println!("交易登录失败: {} - {}", error.error_id, error.error_msg);
                } else if let Some(info) = login_info {
                    println!("交易登录成功: 交易日={}, 前置编号={}, 会话编号={}",
                             info.trading_day, info.front_id, info.session_id);
                }
            }
            TraderEvent::RtnOrder(order) => {
                println!("收到报单回报: 合约={} 方向={:?} 价格={:.2} 数量={} 状态={}",
                         order.instrument_id, order.direction, order.limit_price,
                         order.volume_total_original, order.order_status);
            }
            TraderEvent::RtnTrade(trade) => {
                println!("收到成交回报: 合约={} 方向={:?} 价格={:.2} 数量={} 时间={}",
                         trade.instrument_id, trade.direction, trade.price,
                         trade.volume, trade.trade_time);
            }
            _ => {
                println!("收到其他交易事件: {:?}", event);
            }
        }
    }
}

fn main() -> CtpResult<()> {
    println!("CTP Rust 封装示例程序");
    println!("行情 API 版本: {}", MdApi::get_api_version());
    println!("交易 API 版本: {}", TraderApi::get_api_version());

    // 创建行情 API
    let mut md_api = MdApi::new("md_flow", false, false)?;
    md_api.set_spi_handler(Arc::new(MyMdSpiHandler));

    // 创建交易 API
    let mut trader_api = TraderApi::new("trader_flow")?;
    trader_api.set_spi_handler(Arc::new(MyTraderSpiHandler));

    // 注册前置机地址（使用上期技术提供的测试地址）
    md_api.register_front("tcp://180.168.146.187:10010")?;
    trader_api.register_front("tcp://180.168.146.187:10000")?;

    // 初始化
    md_api.init();
    trader_api.init();

    println!("等待连接建立...");
    thread::sleep(Duration::from_secs(3));

    // 获取交易日
    let md_trading_day = md_api.get_trading_day();
    let trader_trading_day = trader_api.get_trading_day();

    println!("行情交易日: {}", md_trading_day);
    println!("交易交易日: {}", trader_trading_day);

    println!("程序运行中，按 Ctrl+C 退出...");

    // 保持程序运行
    loop {
        thread::sleep(Duration::from_secs(1));
    }
}
