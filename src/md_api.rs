use crate::*;
use crate::error::{CtpError, CtpResult, DisconnectReason};
use crate::types::StringConvert;
use std::ffi::{CStr, CString};
use std::ptr::NonNull;
use std::sync::{Arc, Mutex};
use std::os::raw::{c_char, c_int, c_void};

/// 行情回调事件
#[derive(Debug, Clone)]
pub enum MdEvent {
    /// 前置连接
    FrontConnected,
    /// 前置断开
    FrontDisconnected(DisconnectReason),
    /// 心跳超时警告
    HeartBeatWarning(i32),
    /// 登录响应
    UserLogin {
        login_info: Option<UserLoginInfo>,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 登出响应
    UserLogout {
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 订阅行情响应
    SubMarketData {
        instrument_id: String,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 取消订阅行情响应
    UnSubMarketData {
        instrument_id: String,
        error_info: Option<ErrorInfo>,
        request_id: i32,
        is_last: bool,
    },
    /// 深度行情数据
    DepthMarketData(DepthMarketData),
    /// 询价响应
    ForQuoteRsp(ForQuoteRsp),
    /// 错误响应
    Error {
        error_info: ErrorInfo,
        request_id: i32,
        is_last: bool,
    },
}

/// 用户登录信息
#[derive(Debug, Clone)]
pub struct UserLoginInfo {
    pub trading_day: String,
    pub login_time: String,
    pub broker_id: String,
    pub user_id: String,
    pub system_name: String,
    pub front_id: i32,
    pub session_id: i32,
    pub max_order_ref: String,
    pub shfe_time: String,
    pub dce_time: String,
    pub czce_time: String,
    pub ffex_time: String,
    pub ine_time: String,
    pub sys_version: String,
    pub gfex_time: String,
}

/// 错误信息
#[derive(Debug, Clone)]
pub struct ErrorInfo {
    pub error_id: i32,
    pub error_msg: String,
}

/// 深度行情数据
#[derive(Debug, Clone)]
pub struct DepthMarketData {
    pub trading_day: String,
    pub instrument_id: String,
    pub exchange_id: String,
    pub exchange_inst_id: String,
    pub last_price: f64,
    pub pre_settlement_price: f64,
    pub pre_close_price: f64,
    pub pre_open_interest: f64,
    pub open_price: f64,
    pub highest_price: f64,
    pub lowest_price: f64,
    pub volume: i32,
    pub turnover: f64,
    pub open_interest: f64,
    pub close_price: f64,
    pub settlement_price: f64,
    pub upper_limit_price: f64,
    pub lower_limit_price: f64,
    pub pre_delta: f64,
    pub curr_delta: f64,
    pub update_time: String,
    pub update_millisec: i32,
    pub action_day: String,
    pub bid_price1: f64,
    pub bid_volume1: i32,
    pub ask_price1: f64,
    pub ask_volume1: i32,
    pub bid_price2: f64,
    pub bid_volume2: i32,
    pub ask_price2: f64,
    pub ask_volume2: i32,
    pub bid_price3: f64,
    pub bid_volume3: i32,
    pub ask_price3: f64,
    pub ask_volume3: i32,
    pub bid_price4: f64,
    pub bid_volume4: i32,
    pub ask_price4: f64,
    pub ask_volume4: i32,
    pub bid_price5: f64,
    pub bid_volume5: i32,
    pub ask_price5: f64,
    pub ask_volume5: i32,
    pub average_price: f64,
}

/// 询价响应
#[derive(Debug, Clone)]
pub struct ForQuoteRsp {
    pub trading_day: String,
    pub instrument_id: String,
    pub for_quote_sys_id: String,
    pub for_quote_time: String,
    pub action_day: String,
    pub exchange_id: String,
}

/// 行情回调处理器
pub trait MdSpiHandler: Send + Sync {
    fn on_event(&self, event: MdEvent);
}

/// 行情 API
pub struct MdApi {
    inner: NonNull<ffi::CThostFtdcMdApi>,
    handler: Option<Arc<dyn MdSpiHandler>>,
    request_id: Arc<Mutex<i32>>,
}

unsafe impl Send for MdApi {}
unsafe impl Sync for MdApi {}

impl MdApi {
    /// 创建行情 API
    pub fn new(flow_path: &str, is_using_udp: bool, is_multicast: bool) -> CtpResult<Self> {
        let c_flow_path = flow_path.to_cstring()?;

        unsafe {
            // 使用 autocxx 生成的静态方法
            let api_ptr = ffi::CThostFtdcMdApi_CreateFtdcMdApi(
                c_flow_path.as_ptr(),
                is_using_udp,
                is_multicast
            );

            if api_ptr.is_null() {
                return Err(CtpError::InitializationFailed);
            }

            Ok(Self {
                inner: NonNull::new_unchecked(api_ptr),
                handler: None,
                request_id: Arc::new(Mutex::new(1)),
            })
        }
    }

    /// 获取 API 版本
    pub fn get_api_version() -> String {
        unsafe {
            let version = ffi::CThostFtdcMdApi_GetApiVersion();
            String::from_cstr(CStr::from_ptr(version))
        }
    }
    
    /// 设置回调处理器
    pub fn set_spi_handler(&mut self, handler: Arc<dyn MdSpiHandler>) {
        self.handler = Some(handler);
        // TODO: 注册 SPI 回调
    }
    
    /// 初始化
    pub fn init(&self) {
        unsafe {
            ffi::CThostFtdcMdApi_Init(self.inner.as_ptr());
        }
    }

    /// 等待线程结束
    pub fn join(&self) -> i32 {
        unsafe {
            ffi::CThostFtdcMdApi_Join(self.inner.as_ptr())
        }
    }

    /// 获取交易日
    pub fn get_trading_day(&self) -> String {
        unsafe {
            let trading_day = ffi::CThostFtdcMdApi_GetTradingDay(self.inner.as_ptr());
            String::from_cstr(CStr::from_ptr(trading_day))
        }
    }

    /// 注册前置机地址
    pub fn register_front(&self, front_address: &str) -> CtpResult<()> {
        let c_address = front_address.to_cstring()?;
        unsafe {
            ffi::CThostFtdcMdApi_RegisterFront(self.inner.as_ptr(), c_address.as_ptr() as *mut _);
        }
        Ok(())
    }

    /// 注册名字服务器地址
    pub fn register_name_server(&self, ns_address: &str) -> CtpResult<()> {
        let c_address = ns_address.to_cstring()?;
        unsafe {
            ffi::CThostFtdcMdApi_RegisterNameServer(self.inner.as_ptr(), c_address.as_ptr() as *mut _);
        }
        Ok(())
    }
    
    /// 用户登录
    pub fn req_user_login(&self, broker_id: &str, user_id: &str, password: &str) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造登录请求结构体并调用 API
        // 这里需要使用 autocxx 生成的结构体
        
        Ok(request_id)
    }
    
    /// 用户登出
    pub fn req_user_logout(&self, broker_id: &str, user_id: &str) -> CtpResult<i32> {
        let request_id = self.next_request_id();
        
        // TODO: 构造登出请求结构体并调用 API
        
        Ok(request_id)
    }
    
    /// 订阅行情
    pub fn subscribe_market_data(&self, instrument_ids: &[&str]) -> CtpResult<i32> {
        // TODO: 转换字符串数组并调用 API
        Ok(0)
    }
    
    /// 取消订阅行情
    pub fn unsubscribe_market_data(&self, instrument_ids: &[&str]) -> CtpResult<i32> {
        // TODO: 转换字符串数组并调用 API
        Ok(0)
    }
    
    /// 订阅询价
    pub fn subscribe_for_quote_rsp(&self, instrument_ids: &[&str]) -> CtpResult<i32> {
        // TODO: 转换字符串数组并调用 API
        Ok(0)
    }
    
    /// 取消订阅询价
    pub fn unsubscribe_for_quote_rsp(&self, instrument_ids: &[&str]) -> CtpResult<i32> {
        // TODO: 转换字符串数组并调用 API
        Ok(0)
    }
    
    /// 获取下一个请求 ID
    fn next_request_id(&self) -> i32 {
        let mut id = self.request_id.lock().unwrap();
        let current = *id;
        *id += 1;
        current
    }
}

impl Drop for MdApi {
    fn drop(&mut self) {
        unsafe {
            ffi::CThostFtdcMdApi_Release(self.inner.as_ptr());
        }
    }
}
