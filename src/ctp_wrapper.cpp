#include "ThostFtdcMdApi.h"
#include "ThostFtdcTraderApi.h"
#include "ThostFtdcUserApiStruct.h"
#include "ThostFtdcUserApiDataType.h"

extern "C" {

// ========== 行情 API 包装函数 ==========

// 创建行情 API
CThostFtdcMdApi* ctp_md_api_create(const char* flow_path, bool is_using_udp, bool is_multicast) {
    return CThostFtdcMdApi::CreateFtdcMdApi(flow_path, is_using_udp, is_multicast);
}

// 获取行情 API 版本
const char* ctp_md_api_get_version() {
    return CThostFtdcMdApi::GetApiVersion();
}

// 释放行情 API
void ctp_md_api_release(CThostFtdcMdApi* api) {
    if (api) {
        api->Release();
    }
}

// 初始化行情 API
void ctp_md_api_init(CThostFtdcMdApi* api) {
    if (api) {
        api->Init();
    }
}

// 等待线程结束
int ctp_md_api_join(CThostFtdcMdApi* api) {
    if (api) {
        return api->Join();
    }
    return -1;
}

// 获取交易日
const char* ctp_md_api_get_trading_day(CThostFtdcMdApi* api) {
    if (api) {
        return api->GetTradingDay();
    }
    return nullptr;
}

// 注册前置机地址
void ctp_md_api_register_front(CThostFtdcMdApi* api, char* front_address) {
    if (api) {
        api->RegisterFront(front_address);
    }
}

// 注册名字服务器地址
void ctp_md_api_register_name_server(CThostFtdcMdApi* api, char* ns_address) {
    if (api) {
        api->RegisterNameServer(ns_address);
    }
}

// 注册回调接口
void ctp_md_api_register_spi(CThostFtdcMdApi* api, CThostFtdcMdSpi* spi) {
    if (api) {
        api->RegisterSpi(spi);
    }
}

// 用户登录请求
int ctp_md_api_req_user_login(CThostFtdcMdApi* api, CThostFtdcReqUserLoginField* req, int request_id) {
    if (api) {
        return api->ReqUserLogin(req, request_id);
    }
    return -1;
}

// 用户登出请求
int ctp_md_api_req_user_logout(CThostFtdcMdApi* api, CThostFtdcUserLogoutField* req, int request_id) {
    if (api) {
        return api->ReqUserLogout(req, request_id);
    }
    return -1;
}

// 订阅行情
int ctp_md_api_subscribe_market_data(CThostFtdcMdApi* api, char* instrument_ids[], int count) {
    if (api) {
        return api->SubscribeMarketData(instrument_ids, count);
    }
    return -1;
}

// 取消订阅行情
int ctp_md_api_unsubscribe_market_data(CThostFtdcMdApi* api, char* instrument_ids[], int count) {
    if (api) {
        return api->UnSubscribeMarketData(instrument_ids, count);
    }
    return -1;
}

// 订阅询价
int ctp_md_api_subscribe_for_quote_rsp(CThostFtdcMdApi* api, char* instrument_ids[], int count) {
    if (api) {
        return api->SubscribeForQuoteRsp(instrument_ids, count);
    }
    return -1;
}

// 取消订阅询价
int ctp_md_api_unsubscribe_for_quote_rsp(CThostFtdcMdApi* api, char* instrument_ids[], int count) {
    if (api) {
        return api->UnSubscribeForQuoteRsp(instrument_ids, count);
    }
    return -1;
}

// ========== 交易 API 包装函数 ==========

// 创建交易 API
CThostFtdcTraderApi* ctp_trader_api_create(const char* flow_path) {
    return CThostFtdcTraderApi::CreateFtdcTraderApi(flow_path);
}

// 获取交易 API 版本
const char* ctp_trader_api_get_version() {
    return CThostFtdcTraderApi::GetApiVersion();
}

// 释放交易 API
void ctp_trader_api_release(CThostFtdcTraderApi* api) {
    if (api) {
        api->Release();
    }
}

// 初始化交易 API
void ctp_trader_api_init(CThostFtdcTraderApi* api) {
    if (api) {
        api->Init();
    }
}

// 等待线程结束
int ctp_trader_api_join(CThostFtdcTraderApi* api) {
    if (api) {
        return api->Join();
    }
    return -1;
}

// 获取交易日
const char* ctp_trader_api_get_trading_day(CThostFtdcTraderApi* api) {
    if (api) {
        return api->GetTradingDay();
    }
    return nullptr;
}

// 注册前置机地址
void ctp_trader_api_register_front(CThostFtdcTraderApi* api, char* front_address) {
    if (api) {
        api->RegisterFront(front_address);
    }
}

// 注册名字服务器地址
void ctp_trader_api_register_name_server(CThostFtdcTraderApi* api, char* ns_address) {
    if (api) {
        api->RegisterNameServer(ns_address);
    }
}

// 注册回调接口
void ctp_trader_api_register_spi(CThostFtdcTraderApi* api, CThostFtdcTraderSpi* spi) {
    if (api) {
        api->RegisterSpi(spi);
    }
}

// 客户端认证请求
int ctp_trader_api_req_authenticate(CThostFtdcTraderApi* api, CThostFtdcReqAuthenticateField* req, int request_id) {
    if (api) {
        return api->ReqAuthenticate(req, request_id);
    }
    return -1;
}

// 用户登录请求
int ctp_trader_api_req_user_login(CThostFtdcTraderApi* api, CThostFtdcReqUserLoginField* req, int request_id) {
    if (api) {
        return api->ReqUserLogin(req, request_id);
    }
    return -1;
}

// 用户登出请求
int ctp_trader_api_req_user_logout(CThostFtdcTraderApi* api, CThostFtdcUserLogoutField* req, int request_id) {
    if (api) {
        return api->ReqUserLogout(req, request_id);
    }
    return -1;
}

// 报单录入请求
int ctp_trader_api_req_order_insert(CThostFtdcTraderApi* api, CThostFtdcInputOrderField* req, int request_id) {
    if (api) {
        return api->ReqOrderInsert(req, request_id);
    }
    return -1;
}

// 报单操作请求
int ctp_trader_api_req_order_action(CThostFtdcTraderApi* api, CThostFtdcInputOrderActionField* req, int request_id) {
    if (api) {
        return api->ReqOrderAction(req, request_id);
    }
    return -1;
}

// 查询报单
int ctp_trader_api_req_qry_order(CThostFtdcTraderApi* api, CThostFtdcQryOrderField* req, int request_id) {
    if (api) {
        return api->ReqQryOrder(req, request_id);
    }
    return -1;
}

// 查询成交
int ctp_trader_api_req_qry_trade(CThostFtdcTraderApi* api, CThostFtdcQryTradeField* req, int request_id) {
    if (api) {
        return api->ReqQryTrade(req, request_id);
    }
    return -1;
}

// 查询投资者持仓
int ctp_trader_api_req_qry_investor_position(CThostFtdcTraderApi* api, CThostFtdcQryInvestorPositionField* req, int request_id) {
    if (api) {
        return api->ReqQryInvestorPosition(req, request_id);
    }
    return -1;
}

// 查询资金账户
int ctp_trader_api_req_qry_trading_account(CThostFtdcTraderApi* api, CThostFtdcQryTradingAccountField* req, int request_id) {
    if (api) {
        return api->ReqQryTradingAccount(req, request_id);
    }
    return -1;
}

// 查询合约
int ctp_trader_api_req_qry_instrument(CThostFtdcTraderApi* api, CThostFtdcQryInstrumentField* req, int request_id) {
    if (api) {
        return api->ReqQryInstrument(req, request_id);
    }
    return -1;
}

// 投资者结算结果确认
int ctp_trader_api_req_settlement_info_confirm(CThostFtdcTraderApi* api, CThostFtdcSettlementInfoConfirmField* req, int request_id) {
    if (api) {
        return api->ReqSettlementInfoConfirm(req, request_id);
    }
    return -1;
}

} // extern "C"
