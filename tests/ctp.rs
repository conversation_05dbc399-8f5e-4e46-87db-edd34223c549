use rctp::*;
use std::time::Duration;
use std::sync::Arc;

struct TestMdSpiHandler;

impl MdSpiHandler for TestMdSpiHandler {
    fn on_event(&self, event: MdEvent) {
        println!("测试收到行情事件: {:?}", event);
    }
}

struct TestTraderSpiHandler;

impl TraderSpiHandler for TestTraderSpiHandler {
    fn on_event(&self, event: TraderEvent) {
        println!("测试收到交易事件: {:?}", event);
    }
}

#[test]
fn test_md_api_creation() {
    let result = MdApi::new("test_md_flow", false, false);
    assert!(result.is_ok(), "创建行情 API 应该成功");

    let api_version = MdApi::get_api_version();
    assert!(!api_version.is_empty(), "API 版本不应为空");
    println!("行情 API 版本: {}", api_version);
}

#[test]
fn test_trader_api_creation() {
    let result = TraderApi::new("test_trader_flow");
    assert!(result.is_ok(), "创建交易 API 应该成功");

    let api_version = TraderApi::get_api_version();
    assert!(!api_version.is_empty(), "API 版本不应为空");
    println!("交易 API 版本: {}", api_version);
}

#[test]
fn test_md_api_basic_operations() {
    let mut md_api = MdApi::new("test_md_flow", false, false).expect("创建行情 API 失败");

    // 设置回调处理器
    md_api.set_spi_handler(Arc::new(TestMdSpiHandler));

    // 注册前置机地址
    let result = md_api.register_front("tcp://180.168.146.187:10010");
    assert!(result.is_ok(), "注册前置机地址应该成功");

    // 初始化
    md_api.init();

    // 等待连接建立
    std::thread::sleep(Duration::from_secs(2));

    // 获取交易日（可能为空，因为没有登录）
    let trading_day = md_api.get_trading_day();
    println!("行情交易日: {}", trading_day);
}

#[test]
fn test_trader_api_basic_operations() {
    let mut trader_api = TraderApi::new("test_trader_flow").expect("创建交易 API 失败");

    // 设置回调处理器
    trader_api.set_spi_handler(Arc::new(TestTraderSpiHandler));

    // 注册前置机地址
    let result = trader_api.register_front("tcp://180.168.146.187:10000");
    assert!(result.is_ok(), "注册前置机地址应该成功");

    // 初始化
    trader_api.init();

    // 等待连接建立
    std::thread::sleep(Duration::from_secs(2));

    // 获取交易日（可能为空，因为没有登录）
    let trading_day = trader_api.get_trading_day();
    println!("交易交易日: {}", trading_day);
}

#[test]
fn test_string_conversion() {
    use rctp::types::StringConvert;

    let test_str = "test_string";
    let cstring = test_str.to_cstring().expect("字符串转换失败");
    assert_eq!(cstring.to_str().unwrap(), test_str);

    let bytes = b"test_bytes\0\0\0";
    let converted = String::from_bytes(bytes);
    assert_eq!(converted, "test_bytes");
}

#[test]
fn test_enums() {
    use rctp::types::*;

    // 测试买卖方向
    let buy_direction = Direction::Buy;
    let buy_byte: u8 = buy_direction.into();
    assert_eq!(buy_byte, b'0');

    let sell_direction = Direction::from(b'1');
    assert_eq!(sell_direction, Direction::Sell);

    // 测试开平标志
    let open_flag = OffsetFlag::Open;
    let open_byte: u8 = open_flag.into();
    assert_eq!(open_byte, b'0');

    let close_flag = OffsetFlag::from(b'1');
    assert_eq!(close_flag, OffsetFlag::Close);

    // 测试报单价格条件
    let limit_price = OrderPriceType::LimitPrice;
    let limit_byte: u8 = limit_price.into();
    assert_eq!(limit_byte, b'2');

    let any_price = OrderPriceType::from(b'1');
    assert_eq!(any_price, OrderPriceType::AnyPrice);
}

#[test]
fn test_error_handling() {
    use rctp::error::*;

    // 测试错误创建
    let error = from_rsp_info(3, "登录失败");
    match error {
        CtpError::LoginFailed { error_id, error_msg } => {
            assert_eq!(error_id, 3);
            assert_eq!(error_msg, "登录失败");
        }
        _ => panic!("错误类型不匹配"),
    }

    // 测试检查响应信息
    let result = check_rsp_info(0, "成功");
    assert!(result.is_ok());

    let result = check_rsp_info(1, "失败");
    assert!(result.is_err());

    // 测试断开连接原因
    let reason = DisconnectReason::from(0x1001);
    assert!(matches!(reason, DisconnectReason::NetworkReadFailed));

    let reason_str = format!("{}", reason);
    assert_eq!(reason_str, "网络读失败");
}
