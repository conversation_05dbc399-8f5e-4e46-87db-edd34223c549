use std::env;
use std::path::PathBuf;

fn main() {
    // 设置macOS最低兼容版本为14.0
    #[cfg(target_os = "macos")]
    {
        println!("cargo:rustc-env=MACOSX_DEPLOYMENT_TARGET=14.0");
        println!("cargo:rustc-link-arg=-mmacosx-version-min=14.0");
    }

    let (md_lib_path, trader_lib_path, md_include_path, trader_include_path) = get_platform_paths();
    println!("cargo:warning=MD Include path: {}", md_include_path.display());
    println!("cargo:warning=Trader Include path: {}", trader_include_path.display());

    // 添加C++标准库链接
    #[cfg(not(target_os = "windows"))]
    println!("cargo:rustc-link-lib=c++");

    #[cfg(target_os = "windows")]
    println!("cargo:rustc-link-lib=msvcrt");

    // 设置库路径和链接选项
    setup_platform_linking(&md_lib_path, &trader_lib_path);

    // 生成绑定
    generate_bindings(&md_include_path, &trader_include_path);

    // 确保文件变化时重新运行
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=wrapper.h");
}

fn generate_bindings(md_include_path: &PathBuf, trader_include_path: &PathBuf) {
    // 创建包装头文件
    let wrapper_content = r#"
#include "ThostFtdcMdApi.h"
#include "ThostFtdcTraderApi.h"
#include "ThostFtdcUserApiStruct.h"
#include "ThostFtdcUserApiDataType.h"
"#;

    std::fs::write("wrapper.h", wrapper_content).expect("无法创建包装头文件");

    let mut builder = bindgen::Builder::default()
        .header("wrapper.h")
        .clang_arg(format!("-I{}", md_include_path.display()))
        .clang_arg(format!("-I{}", trader_include_path.display()))
        .clang_arg("-x")
        .clang_arg("c++")
        .clang_arg("-std=c++17");

    // 平台特定的编译参数
    #[cfg(target_os = "macos")]
    {
        builder = builder
            .clang_arg(format!("-F{}", md_include_path.parent().unwrap().display()))
            .clang_arg(format!("-F{}", trader_include_path.parent().unwrap().display()))
            .clang_arg("-DLIB_MD_API_EXPORT")
            .clang_arg("-DMD_API_EXPORT=__attribute__((visibility(\"default\")))")
            .clang_arg("-DLIB_TRADER_API_EXPORT")
            .clang_arg("-DTRADER_API_EXPORT=__attribute__((visibility(\"default\")))");
    }

    let bindings = builder
        .parse_callbacks(Box::new(bindgen::CargoCallbacks::new()))
        .generate()
        .expect("无法生成绑定");

    let out_path = PathBuf::from(env::var("OUT_DIR").unwrap());
    bindings
        .write_to_file(out_path.join("bindings.rs"))
        .expect("无法写入绑定文件");
}

#[cfg(target_os = "macos")]
fn get_platform_paths() -> (PathBuf, PathBuf, PathBuf, PathBuf) {
    let base = PathBuf::from("assets/macos");
    let md_lib = base.join("thostmduserapi_se.framework");
    let trader_lib = base.join("thosttraderapi_se.framework");
    let md_include = md_lib.join("Versions/A/Headers");
    let trader_include = trader_lib.join("Versions/A/Headers");
    (md_lib, trader_lib, md_include, trader_include)
}

#[cfg(target_os = "windows")]
fn get_platform_paths() -> (PathBuf, PathBuf, PathBuf, PathBuf) {
    let base = PathBuf::from("assets/windows");
    let lib = base.join("lib");
    let include = base.join("include");
    (lib.clone(), lib.clone(), include.clone(), include)
}

#[cfg(target_os = "linux")]
fn get_platform_paths() -> (PathBuf, PathBuf, PathBuf, PathBuf) {
    let base = PathBuf::from("assets/linux");
    let lib = base.join("lib");
    let include = base.join("include");
    (lib.clone(), lib.clone(), include.clone(), include)
}

#[cfg(not(any(target_os = "macos", target_os = "windows", target_os = "linux")))]
fn get_platform_paths() -> (PathBuf, PathBuf, PathBuf, PathBuf) {
    panic!("Unsupported operating system")
}

#[cfg(target_os = "macos")]
fn setup_platform_linking(md_lib_path: &PathBuf, trader_lib_path: &PathBuf) {
    println!("cargo:rustc-link-search=framework={}", md_lib_path.display());
    println!("cargo:rustc-link-search=framework={}", trader_lib_path.display());
    println!("cargo:rustc-link-lib=framework=thostmduserapi_se");
    println!("cargo:rustc-link-lib=framework=thosttraderapi_se");
    println!("cargo:rustc-link-arg=-Wl,-rpath,{}", md_lib_path.display());
    println!("cargo:rustc-link-arg=-Wl,-rpath,{}", trader_lib_path.display());
}

#[cfg(target_os = "windows")]
fn setup_platform_linking(md_lib_path: &PathBuf, _trader_lib_path: &PathBuf) {
    println!("cargo:rustc-link-search={}", md_lib_path.display());
    println!("cargo:rustc-link-lib=thostmduserapi_se");
    println!("cargo:rustc-link-lib=thosttraderapi_se");
}

#[cfg(target_os = "linux")]
fn setup_platform_linking(md_lib_path: &PathBuf, _trader_lib_path: &PathBuf) {
    println!("cargo:rustc-link-search={}", md_lib_path.display());
    println!("cargo:rustc-link-lib=thostmduserapi_se");
    println!("cargo:rustc-link-lib=thosttraderapi_se");
}

#[cfg(not(any(target_os = "macos", target_os = "windows", target_os = "linux")))]
fn setup_platform_linking(_md_lib_path: &PathBuf, _trader_lib_path: &PathBuf) {
    panic!("Unsupported operating system")
}
