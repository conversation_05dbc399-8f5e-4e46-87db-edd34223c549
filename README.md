# RCTP - Rust CTP API 封装

一个使用 Rust + autocxx 封装的上期技术 CTP API 库，提供类型安全的期货交易和行情接口。

## 特性

- ✅ **完整的 API 封装**: 支持行情 API (MdApi) 和交易 API (TraderApi)
- ✅ **类型安全**: 使用 Rust 类型系统确保内存安全和线程安全
- ✅ **跨平台支持**: 支持 Windows、Linux 和 macOS
- ✅ **异步回调**: 基于事件驱动的回调机制
- ✅ **错误处理**: 完善的错误类型和处理机制
- ✅ **易于使用**: 提供 Rust 风格的 API 接口

## 系统要求

- Rust 1.70+
- C++ 编译器 (支持 C++17)
- CTP API 库文件

## 安装

将以下内容添加到您的 `Cargo.toml`:

```toml
[dependencies]
rctp = "0.1.0"
```

## 快速开始

### 行情 API 使用示例

```rust
use rctp::*;
use std::sync::Arc;

// 实现行情回调处理器
struct MyMdHandler;

impl MdSpiHandler for MyMdHandler {
    fn on_event(&self, event: MdEvent) {
        match event {
            MdEvent::FrontConnected => {
                println!("行情前置连接成功");
            }
            MdEvent::DepthMarketData(data) => {
                println!("收到行情: {} 最新价={:.2}",
                         data.instrument_id, data.last_price);
            }
            _ => {}
        }
    }
}

fn main() -> CtpResult<()> {
    // 创建行情 API
    let mut md_api = MdApi::new("md_flow", false, false)?;

    // 设置回调处理器
    md_api.set_spi_handler(Arc::new(MyMdHandler));

    // 注册前置机地址
    md_api.register_front("tcp://180.168.146.187:10010")?;

    // 初始化并连接
    md_api.init();

    // 登录
    let request_id = md_api.req_user_login("9999", "your_user_id", "your_password")?;

    // 订阅行情
    md_api.subscribe_market_data(&["rb2405", "au2406"])?;

    // 保持程序运行
    md_api.join();

    Ok(())
}
```

### 交易 API 使用示例

```rust
use rctp::*;
use std::sync::Arc;

// 实现交易回调处理器
struct MyTraderHandler;

impl TraderSpiHandler for MyTraderHandler {
    fn on_event(&self, event: TraderEvent) {
        match event {
            TraderEvent::FrontConnected => {
                println!("交易前置连接成功");
            }
            TraderEvent::UserLogin { login_info, error_info, .. } => {
                if let Some(error) = error_info {
                    println!("登录失败: {}", error.error_msg);
                } else {
                    println!("登录成功");
                }
            }
            TraderEvent::RtnOrder(order) => {
                println!("报单回报: {} 状态={}",
                         order.instrument_id, order.order_status);
            }
            TraderEvent::RtnTrade(trade) => {
                println!("成交回报: {} 价格={:.2} 数量={}",
                         trade.instrument_id, trade.price, trade.volume);
            }
            _ => {}
        }
    }
}

fn main() -> CtpResult<()> {
    // 创建交易 API
    let mut trader_api = TraderApi::new("trader_flow")?;

    // 设置回调处理器
    trader_api.set_spi_handler(Arc::new(MyTraderHandler));

    // 注册前置机地址
    trader_api.register_front("tcp://180.168.146.187:10000")?;

    // 初始化并连接
    trader_api.init();

    // 客户端认证
    trader_api.req_authenticate("9999", "your_user_id", "your_product_info",
                               "your_auth_code", "your_app_id")?;

    // 用户登录
    trader_api.req_user_login("9999", "your_user_id", "your_password")?;

    // 下单示例
    let order = InputOrder {
        broker_id: "9999".to_string(),
        investor_id: "your_investor_id".to_string(),
        instrument_id: "rb2405".to_string(),
        order_ref: "1".to_string(),
        user_id: "your_user_id".to_string(),
        order_price_type: OrderPriceType::LimitPrice,
        direction: Direction::Buy,
        comb_offset_flag: "0".to_string(),
        comb_hedge_flag: "1".to_string(),
        limit_price: 3500.0,
        volume_total_original: 1,
        time_condition: TimeCondition::GFD,
        volume_condition: VolumeCondition::AV,
        contingent_condition: ContingentCondition::Immediately,
        // ... 其他字段
        ..Default::default()
    };

    trader_api.req_order_insert(&order)?;

    // 保持程序运行
    trader_api.join();

    Ok(())
}
```

## API 文档

### 行情 API (MdApi)

#### 主要方法

- `new(flow_path, is_using_udp, is_multicast)` - 创建行情 API 实例
- `register_front(address)` - 注册前置机地址
- `req_user_login(broker_id, user_id, password)` - 用户登录
- `subscribe_market_data(instrument_ids)` - 订阅行情
- `unsubscribe_market_data(instrument_ids)` - 取消订阅行情

#### 回调事件

- `FrontConnected` - 前置连接成功
- `FrontDisconnected` - 前置断开连接
- `UserLogin` - 登录响应
- `DepthMarketData` - 深度行情数据
- `SubMarketData` - 订阅行情响应

### 交易 API (TraderApi)

#### 主要方法

- `new(flow_path)` - 创建交易 API 实例
- `register_front(address)` - 注册前置机地址
- `req_authenticate(...)` - 客户端认证
- `req_user_login(broker_id, user_id, password)` - 用户登录
- `req_order_insert(order)` - 报单录入
- `req_order_action(order_action)` - 报单操作
- `req_qry_order(...)` - 查询报单
- `req_qry_trade(...)` - 查询成交
- `req_qry_investor_position(...)` - 查询持仓
- `req_qry_trading_account(...)` - 查询资金账户

#### 回调事件

- `FrontConnected` - 前置连接成功
- `FrontDisconnected` - 前置断开连接
- `Authenticate` - 认证响应
- `UserLogin` - 登录响应
- `OrderInsert` - 报单录入响应
- `RtnOrder` - 报单回报
- `RtnTrade` - 成交回报

## 数据类型

### 枚举类型

- `Direction` - 买卖方向 (Buy/Sell)
- `OffsetFlag` - 开平标志 (Open/Close/CloseToday/CloseYesterday)
- `OrderPriceType` - 报单价格条件 (AnyPrice/LimitPrice/BestPrice)
- `TimeCondition` - 有效期类型 (IOC/GFD/GTC)
- `VolumeCondition` - 成交量类型 (AV/MV/CV)

### 结构体

- `DepthMarketData` - 深度行情数据
- `InputOrder` - 报单录入
- `Order` - 报单
- `Trade` - 成交
- `InvestorPosition` - 投资者持仓
- `TradingAccount` - 资金账户
- `Instrument` - 合约信息

## 错误处理

库提供了完善的错误处理机制：

```rust
use rctp::error::*;

match api_result {
    Ok(request_id) => println!("请求成功，ID: {}", request_id),
    Err(CtpError::LoginFailed { error_id, error_msg }) => {
        println!("登录失败: {} - {}", error_id, error_msg);
    }
    Err(CtpError::ConnectionFailed(msg)) => {
        println!("连接失败: {}", msg);
    }
    Err(e) => println!("其他错误: {}", e),
}
```

## 构建和测试

```bash
# 构建项目
cargo build

# 运行测试
cargo test

# 运行示例
cargo run
```

## 平台支持

### macOS
- 支持 macOS 14.0+
- 使用 Framework 格式的 CTP 库

### Windows
- 支持 Windows 10+
- 使用 DLL 格式的 CTP 库

### Linux
- 支持主流 Linux 发行版
- 使用 SO 格式的 CTP 库

## 注意事项

1. **账户信息**: 示例中的账户信息需要替换为您的真实账户信息
2. **前置地址**: 使用您的期货公司提供的前置机地址
3. **认证信息**: 交易需要先进行客户端认证
4. **风险控制**: 请在测试环境中充分测试后再用于生产环境

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 参考资料

- [CTP API 官方文档](http://www.sfit.com.cn/5_2_DocumentDown.htm)
- [autocxx 文档](https://google.github.io/autocxx/)
- [Rust FFI 指南](https://doc.rust-lang.org/nomicon/ffi.html)

[Rust 调用 C/Rust 生成的动态库](https://yanbin.blog/rust-call-c-rust-shared-library/)

## rust和cxx互操作项目

[GitHub Interop Rust And CXX](https://github.com/search?q=rust+cxx&type=repositories&s=stars&o=desc)

[autocxx](https://github.com/google/autocxx)

[cxx](https://github.com/dtolnay/cxx)

[rust_libloading](https://github.com/nagisa/rust_libloading/)

[xapian-rs](https://github.com/torrancew/xapian-rs)

## macos framework bundle介绍

[MacOs - 系统理解 iOS 库与框架 ](https://www.cnblogs.com/zhuchunlin/p/17943571)
- cocoa touch framework